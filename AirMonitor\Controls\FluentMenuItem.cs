using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace AirMonitor.Controls;

/// <summary>
/// Fluent Design 菜单项控件
/// </summary>
public class FluentMenuItem : Control
{
    #region 依赖属性

    /// <summary>
    /// 菜单项文本
    /// </summary>
    public static readonly DependencyProperty HeaderProperty =
        DependencyProperty.Register(
            nameof(Header),
            typeof(string),
            typeof(FluentMenuItem),
            new PropertyMetadata(string.Empty));

    /// <summary>
    /// 菜单项图标
    /// </summary>
    public static readonly DependencyProperty IconProperty =
        DependencyProperty.Register(
            nameof(Icon),
            typeof(Geometry),
            typeof(FluentMenuItem),
            new PropertyMetadata(null));

    /// <summary>
    /// 快捷键文本
    /// </summary>
    public static readonly DependencyProperty InputGestureTextProperty =
        DependencyProperty.Register(
            nameof(InputGestureText),
            typeof(string),
            typeof(FluentMenuItem),
            new PropertyMetadata(string.Empty));

    /// <summary>
    /// 访问键
    /// </summary>
    public static readonly DependencyProperty AccessKeyProperty =
        DependencyProperty.Register(
            nameof(AccessKey),
            typeof(string),
            typeof(FluentMenuItem),
            new PropertyMetadata(string.Empty));

    /// <summary>
    /// 是否为分隔符
    /// </summary>
    public static readonly DependencyProperty IsSeparatorProperty =
        DependencyProperty.Register(
            nameof(IsSeparator),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    /// <summary>
    /// 是否可选中
    /// </summary>
    public static readonly DependencyProperty IsCheckableProperty =
        DependencyProperty.Register(
            nameof(IsCheckable),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    /// <summary>
    /// 是否已选中
    /// </summary>
    public static readonly DependencyProperty IsCheckedProperty =
        DependencyProperty.Register(
            nameof(IsChecked),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    /// <summary>
    /// 子菜单项集合
    /// </summary>
    public static readonly DependencyProperty ItemsProperty =
        DependencyProperty.Register(
            nameof(Items),
            typeof(ObservableCollection<FluentMenuItem>),
            typeof(FluentMenuItem),
            new PropertyMetadata(null));

    /// <summary>
    /// 是否打开子菜单
    /// </summary>
    public static readonly DependencyProperty IsSubmenuOpenProperty =
        DependencyProperty.Register(
            nameof(IsSubmenuOpen),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    /// <summary>
    /// 是否获得焦点
    /// </summary>
    public static readonly DependencyProperty IsFocusedProperty =
        DependencyProperty.Register(
            nameof(IsFocused),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    /// <summary>
    /// 命令
    /// </summary>
    public static readonly DependencyProperty CommandProperty =
        DependencyProperty.Register(
            nameof(Command),
            typeof(ICommand),
            typeof(FluentMenuItem),
            new PropertyMetadata(null));

    /// <summary>
    /// 命令参数
    /// </summary>
    public static readonly DependencyProperty CommandParameterProperty =
        DependencyProperty.Register(
            nameof(CommandParameter),
            typeof(object),
            typeof(FluentMenuItem),
            new PropertyMetadata(null));

    /// <summary>
    /// 是否被按下
    /// </summary>
    public static readonly DependencyProperty IsPressedProperty =
        DependencyProperty.Register(
            nameof(IsPressed),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    /// <summary>
    /// 是否鼠标悬停
    /// </summary>
    public static readonly DependencyProperty IsMouseOverProperty =
        DependencyProperty.Register(
            nameof(IsMouseOver),
            typeof(bool),
            typeof(FluentMenuItem),
            new PropertyMetadata(false));

    #endregion

    #region 属性

    /// <summary>
    /// 菜单项文本
    /// </summary>
    public string Header
    {
        get => (string)GetValue(HeaderProperty);
        set => SetValue(HeaderProperty, value);
    }

    /// <summary>
    /// 菜单项图标
    /// </summary>
    public Geometry Icon
    {
        get => (Geometry)GetValue(IconProperty);
        set => SetValue(IconProperty, value);
    }

    /// <summary>
    /// 快捷键文本
    /// </summary>
    public string InputGestureText
    {
        get => (string)GetValue(InputGestureTextProperty);
        set => SetValue(InputGestureTextProperty, value);
    }

    /// <summary>
    /// 访问键
    /// </summary>
    public string AccessKey
    {
        get => (string)GetValue(AccessKeyProperty);
        set => SetValue(AccessKeyProperty, value);
    }

    /// <summary>
    /// 是否为分隔符
    /// </summary>
    public bool IsSeparator
    {
        get => (bool)GetValue(IsSeparatorProperty);
        set => SetValue(IsSeparatorProperty, value);
    }

    /// <summary>
    /// 是否可选中
    /// </summary>
    public bool IsCheckable
    {
        get => (bool)GetValue(IsCheckableProperty);
        set => SetValue(IsCheckableProperty, value);
    }

    /// <summary>
    /// 是否已选中
    /// </summary>
    public bool IsChecked
    {
        get => (bool)GetValue(IsCheckedProperty);
        set => SetValue(IsCheckedProperty, value);
    }

    /// <summary>
    /// 子菜单项集合
    /// </summary>
    public ObservableCollection<FluentMenuItem> Items
    {
        get => (ObservableCollection<FluentMenuItem>)GetValue(ItemsProperty);
        set => SetValue(ItemsProperty, value);
    }

    /// <summary>
    /// 是否打开子菜单
    /// </summary>
    public bool IsSubmenuOpen
    {
        get => (bool)GetValue(IsSubmenuOpenProperty);
        set => SetValue(IsSubmenuOpenProperty, value);
    }

    /// <summary>
    /// 是否获得焦点
    /// </summary>
    public new bool IsFocused
    {
        get => (bool)GetValue(IsFocusedProperty);
        set => SetValue(IsFocusedProperty, value);
    }

    /// <summary>
    /// 命令
    /// </summary>
    public ICommand Command
    {
        get => (ICommand)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    /// <summary>
    /// 命令参数
    /// </summary>
    public object CommandParameter
    {
        get => GetValue(CommandParameterProperty);
        set => SetValue(CommandParameterProperty, value);
    }

    /// <summary>
    /// 是否被按下
    /// </summary>
    public bool IsPressed
    {
        get => (bool)GetValue(IsPressedProperty);
        set => SetValue(IsPressedProperty, value);
    }

    /// <summary>
    /// 是否鼠标悬停
    /// </summary>
    public new bool IsMouseOver
    {
        get => (bool)GetValue(IsMouseOverProperty);
        set => SetValue(IsMouseOverProperty, value);
    }

    /// <summary>
    /// 是否有子菜单项
    /// </summary>
    public bool HasItems => Items != null && Items.Count > 0;

    #endregion

    #region 路由事件

    /// <summary>
    /// 点击事件
    /// </summary>
    public static readonly RoutedEvent ClickEvent =
        EventManager.RegisterRoutedEvent(
            nameof(Click),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(FluentMenuItem));

    /// <summary>
    /// 点击事件
    /// </summary>
    public event RoutedEventHandler Click
    {
        add => AddHandler(ClickEvent, value);
        remove => RemoveHandler(ClickEvent, value);
    }

    #endregion

    #region 构造函数

    static FluentMenuItem()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(FluentMenuItem), new FrameworkPropertyMetadata(typeof(FluentMenuItem)));
    }

    public FluentMenuItem()
    {
        Items = new ObservableCollection<FluentMenuItem>();
    }

    #endregion

    #region 重写方法

    protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
    {
        if (!IsSeparator && IsEnabled)
        {
            IsPressed = true;
            CaptureMouse();
            e.Handled = true;
        }
        base.OnMouseLeftButtonDown(e);
    }

    protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
    {
        if (IsPressed)
        {
            IsPressed = false;
            ReleaseMouseCapture();

            if (IsMouseOver && !IsSeparator && IsEnabled)
            {
                RaiseClickEvent();
            }
            e.Handled = true;
        }
        base.OnMouseLeftButtonUp(e);
    }

    protected override void OnMouseEnter(MouseEventArgs e)
    {
        IsMouseOver = true;
        base.OnMouseEnter(e);
    }

    protected override void OnMouseLeave(MouseEventArgs e)
    {
        IsMouseOver = false;
        if (IsPressed)
        {
            IsPressed = false;
            ReleaseMouseCapture();
        }
        base.OnMouseLeave(e);
    }

    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.Enter || e.Key == Key.Space)
        {
            if (!IsSeparator && IsEnabled)
            {
                RaiseClickEvent();
                e.Handled = true;
            }
        }
        base.OnKeyDown(e);
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 触发点击事件
    /// </summary>
    public void RaiseClickEvent()
    {
        if (IsSeparator || !IsEnabled)
            return;

        // 处理可选中项
        if (IsCheckable)
        {
            IsChecked = !IsChecked;
        }

        // 执行命令
        if (Command != null && Command.CanExecute(CommandParameter))
        {
            Command.Execute(CommandParameter);
        }

        // 触发点击事件
        var args = new RoutedEventArgs(ClickEvent, this);
        RaiseEvent(args);
    }

    #endregion
}
