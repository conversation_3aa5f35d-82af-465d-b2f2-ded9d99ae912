﻿<Application
    x:Class="AirMonitor.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:AirMonitor">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  基础色彩定义  -->
                <ResourceDictionary Source="Styles/Colors.xaml" />
                <!--  字体系统定义  -->
                <ResourceDictionary Source="Styles/Fonts.xaml" />
                <!--  菜单样式定义  -->
                <ResourceDictionary Source="Styles/MenuStyles.xaml" />
                <!--  默认使用亮色主题，运行时会被ThemeService替换  -->
                <ResourceDictionary Source="Styles/Themes/Light.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
