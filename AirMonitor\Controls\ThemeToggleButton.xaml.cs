using System.Windows;
using System.Windows.Controls;
using AirMonitor.Services;
using Microsoft.Extensions.DependencyInjection;

namespace AirMonitor.Controls;

/// <summary>
/// 主题切换按钮用户控件
/// </summary>
public partial class ThemeToggleButton : UserControl
{
    private IThemeService? _themeService;

    public ThemeToggleButton()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 获取主题服务实例
        if (Application.Current is App app && app.ServiceProvider != null)
        {
            _themeService = app.ServiceProvider.GetService<IThemeService>();
            if (_themeService != null)
            {
                _themeService.ThemeChanged += OnThemeChanged;
                UpdateIcon(_themeService.CurrentTheme);
            }
        }
    }

    private void ToggleButton_Click(object sender, RoutedEventArgs e)
    {
        _themeService?.ToggleTheme();
    }

    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        Dispatcher.BeginInvoke(() => UpdateIcon(e.NewTheme));
    }

    private void UpdateIcon(ThemeType theme)
    {
        var actualTheme = theme == ThemeType.System ? GetSystemTheme() : theme;
        
        if (actualTheme == ThemeType.Light)
        {
            SunIcon.Visibility = Visibility.Visible;
            MoonIcon.Visibility = Visibility.Collapsed;
            ToggleButton.ToolTip = "切换到暗色主题";
        }
        else
        {
            SunIcon.Visibility = Visibility.Collapsed;
            MoonIcon.Visibility = Visibility.Visible;
            ToggleButton.ToolTip = "切换到亮色主题";
        }
    }

    private static ThemeType GetSystemTheme()
    {
        try
        {
            using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
            var value = key?.GetValue("AppsUseLightTheme");
            
            if (value is int intValue)
            {
                return intValue == 1 ? ThemeType.Light : ThemeType.Dark;
            }
        }
        catch
        {
            // 忽略错误，返回默认值
        }

        return ThemeType.Light;
    }
}
