<UserControl x:Class="AirMonitor.Controls.ThemeToggleButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="40">
    
    <UserControl.Resources>
        <!-- 主题切换按钮样式 -->
        <Style x:Key="ThemeToggleButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" 
                                        Value="{DynamicResource ControlFillColorDefaultHoverBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" 
                                        Value="{DynamicResource ControlFillColorDefaultPressedBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 图标样式 -->
        <Style x:Key="ThemeIconStyle" TargetType="Path">
            <Setter Property="Fill" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
        </Style>
    </UserControl.Resources>

    <Button x:Name="ToggleButton"
            Style="{StaticResource ThemeToggleButtonStyle}"
            Click="ToggleButton_Click"
            ToolTip="切换主题">
        <Grid>
            <!-- 太阳图标 (亮色主题) -->
            <Path x:Name="SunIcon"
                  Style="{StaticResource ThemeIconStyle}"
                  Data="M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M12,4L10.5,2.5L13.5,2.5L12,4M20,10.5V13.5H22V10.5H20M4,10.5V13.5H2V10.5H4M15.5,6.5L14,5L16,3L17.5,4.5L15.5,6.5M8.5,6.5L6.5,4.5L8,3L10,5L8.5,6.5M15.5,17.5L17.5,19.5L16,21L14,19L15.5,17.5M8.5,17.5L10,19L8,21L6.5,19.5L8.5,17.5M12,22L13.5,21.5L10.5,21.5L12,22Z"
                  Visibility="Visible"/>
            
            <!-- 月亮图标 (暗色主题) -->
            <Path x:Name="MoonIcon"
                  Style="{StaticResource ThemeIconStyle}"
                  Data="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95Z"
                  Visibility="Collapsed"/>
        </Grid>
    </Button>
</UserControl>
