using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace AirMonitor.Controls;

/// <summary>
/// Fluent Design 菜单栏控件
/// 符合Windows 11设计规范的自定义菜单栏
/// </summary>
public class FluentMenuBar : Control
{
    #region 依赖属性

    /// <summary>
    /// 菜单项集合
    /// </summary>
    public static readonly DependencyProperty MenuItemsProperty =
        DependencyProperty.Register(
            nameof(MenuItems),
            typeof(ObservableCollection<FluentMenuItem>),
            typeof(FluentMenuBar),
            new PropertyMetadata(null, OnMenuItemsChanged));

    /// <summary>
    /// 是否启用键盘导航
    /// </summary>
    public static readonly DependencyProperty IsKeyboardNavigationEnabledProperty =
        DependencyProperty.Register(
            nameof(IsKeyboardNavigationEnabled),
            typeof(bool),
            typeof(FluentMenuBar),
            new PropertyMetadata(true));

    /// <summary>
    /// 菜单栏高度
    /// </summary>
    public static readonly DependencyProperty MenuBarHeightProperty =
        DependencyProperty.Register(
            nameof(MenuBarHeight),
            typeof(double),
            typeof(FluentMenuBar),
            new PropertyMetadata(32.0));

    /// <summary>
    /// 是否显示下划线访问键
    /// </summary>
    public static readonly DependencyProperty ShowAccessKeysProperty =
        DependencyProperty.Register(
            nameof(ShowAccessKeys),
            typeof(bool),
            typeof(FluentMenuBar),
            new PropertyMetadata(false));

    #endregion

    #region 属性

    /// <summary>
    /// 菜单项集合
    /// </summary>
    public ObservableCollection<FluentMenuItem> MenuItems
    {
        get => (ObservableCollection<FluentMenuItem>)GetValue(MenuItemsProperty);
        set => SetValue(MenuItemsProperty, value);
    }

    /// <summary>
    /// 是否启用键盘导航
    /// </summary>
    public bool IsKeyboardNavigationEnabled
    {
        get => (bool)GetValue(IsKeyboardNavigationEnabledProperty);
        set => SetValue(IsKeyboardNavigationEnabledProperty, value);
    }

    /// <summary>
    /// 菜单栏高度
    /// </summary>
    public double MenuBarHeight
    {
        get => (double)GetValue(MenuBarHeightProperty);
        set => SetValue(MenuBarHeightProperty, value);
    }

    /// <summary>
    /// 是否显示下划线访问键
    /// </summary>
    public bool ShowAccessKeys
    {
        get => (bool)GetValue(ShowAccessKeysProperty);
        set => SetValue(ShowAccessKeysProperty, value);
    }

    #endregion

    #region 路由事件

    /// <summary>
    /// 菜单项点击事件
    /// </summary>
    public static readonly RoutedEvent MenuItemClickEvent =
        EventManager.RegisterRoutedEvent(
            nameof(MenuItemClick),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(FluentMenuBar));

    /// <summary>
    /// 菜单项点击事件
    /// </summary>
    public event RoutedEventHandler MenuItemClick
    {
        add => AddHandler(MenuItemClickEvent, value);
        remove => RemoveHandler(MenuItemClickEvent, value);
    }

    #endregion

    #region 字段

    private int _currentFocusedIndex = -1;
    private bool _isMenuMode = false;

    #endregion

    #region 构造函数

    static FluentMenuBar()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(FluentMenuBar), new FrameworkPropertyMetadata(typeof(FluentMenuBar)));
    }

    public FluentMenuBar()
    {
        MenuItems = new ObservableCollection<FluentMenuItem>();
        Focusable = true;
        KeyboardNavigation.SetTabNavigation(this, KeyboardNavigationMode.Cycle);
        KeyboardNavigation.SetDirectionalNavigation(this, KeyboardNavigationMode.Cycle);
    }

    #endregion

    #region 重写方法

    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (!IsKeyboardNavigationEnabled)
        {
            base.OnKeyDown(e);
            return;
        }

        switch (e.Key)
        {
            case Key.LeftAlt:
            case Key.RightAlt:
            case Key.F10:
                ToggleMenuMode();
                e.Handled = true;
                break;

            case Key.Left:
                if (_isMenuMode)
                {
                    NavigateLeft();
                    e.Handled = true;
                }
                break;

            case Key.Right:
                if (_isMenuMode)
                {
                    NavigateRight();
                    e.Handled = true;
                }
                break;

            case Key.Down:
                if (_isMenuMode && _currentFocusedIndex >= 0)
                {
                    OpenCurrentMenu();
                    e.Handled = true;
                }
                break;

            case Key.Enter:
            case Key.Space:
                if (_isMenuMode && _currentFocusedIndex >= 0)
                {
                    OpenCurrentMenu();
                    e.Handled = true;
                }
                break;

            case Key.Escape:
                if (_isMenuMode)
                {
                    ExitMenuMode();
                    e.Handled = true;
                }
                break;

            default:
                // 处理访问键
                if (_isMenuMode && e.Key >= Key.A && e.Key <= Key.Z)
                {
                    HandleAccessKey(e.Key);
                    e.Handled = true;
                }
                break;
        }

        base.OnKeyDown(e);
    }

    protected override void OnLostFocus(RoutedEventArgs e)
    {
        ExitMenuMode();
        base.OnLostFocus(e);
    }

    #endregion

    #region 私有方法

    private static void OnMenuItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FluentMenuBar menuBar)
        {
            if (e.OldValue is ObservableCollection<FluentMenuItem> oldCollection)
            {
                oldCollection.CollectionChanged -= menuBar.OnMenuItemsCollectionChanged;
            }

            if (e.NewValue is ObservableCollection<FluentMenuItem> newCollection)
            {
                newCollection.CollectionChanged += menuBar.OnMenuItemsCollectionChanged;
            }
        }
    }

    private void OnMenuItemsCollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
    {
        // 重新绑定菜单项事件
        if (e.OldItems != null)
        {
            foreach (FluentMenuItem item in e.OldItems)
            {
                item.Click -= OnMenuItemClick;
            }
        }

        if (e.NewItems != null)
        {
            foreach (FluentMenuItem item in e.NewItems)
            {
                item.Click += OnMenuItemClick;
            }
        }
    }

    private void OnMenuItemClick(object sender, RoutedEventArgs e)
    {
        var args = new RoutedEventArgs(MenuItemClickEvent, sender);
        RaiseEvent(args);
    }

    private void ToggleMenuMode()
    {
        _isMenuMode = !_isMenuMode;
        ShowAccessKeys = _isMenuMode;

        if (_isMenuMode)
        {
            Focus();
            _currentFocusedIndex = 0;
            UpdateFocusVisual();
        }
        else
        {
            _currentFocusedIndex = -1;
            UpdateFocusVisual();
        }
    }

    private void ExitMenuMode()
    {
        _isMenuMode = false;
        ShowAccessKeys = false;
        _currentFocusedIndex = -1;
        UpdateFocusVisual();
    }

    private void NavigateLeft()
    {
        if (MenuItems.Count == 0) return;

        _currentFocusedIndex--;
        if (_currentFocusedIndex < 0)
            _currentFocusedIndex = MenuItems.Count - 1;

        UpdateFocusVisual();
    }

    private void NavigateRight()
    {
        if (MenuItems.Count == 0) return;

        _currentFocusedIndex++;
        if (_currentFocusedIndex >= MenuItems.Count)
            _currentFocusedIndex = 0;

        UpdateFocusVisual();
    }

    private void OpenCurrentMenu()
    {
        if (_currentFocusedIndex >= 0 && _currentFocusedIndex < MenuItems.Count)
        {
            var menuItem = MenuItems[_currentFocusedIndex];
            if (menuItem.HasItems)
            {
                menuItem.IsSubmenuOpen = true;
            }
            else
            {
                menuItem.RaiseClickEvent();
                ExitMenuMode();
            }
        }
    }

    private void HandleAccessKey(Key key)
    {
        var accessKey = key.ToString().ToLower();
        for (int i = 0; i < MenuItems.Count; i++)
        {
            var menuItem = MenuItems[i];
            if (menuItem.AccessKey?.ToLower() == accessKey)
            {
                _currentFocusedIndex = i;
                OpenCurrentMenu();
                break;
            }
        }
    }

    private void UpdateFocusVisual()
    {
        for (int i = 0; i < MenuItems.Count; i++)
        {
            MenuItems[i].IsFocused = (_isMenuMode && i == _currentFocusedIndex);
        }
    }

    #endregion
}
