using System;

namespace AirMonitor.Services;

/// <summary>
/// 主题服务接口
/// 提供主题切换和管理功能
/// </summary>
public interface IThemeService
{
    /// <summary>
    /// 当前主题类型
    /// </summary>
    ThemeType CurrentTheme { get; }
    
    /// <summary>
    /// 主题变更事件
    /// </summary>
    event EventHandler<ThemeChangedEventArgs> ThemeChanged;
    
    /// <summary>
    /// 设置主题
    /// </summary>
    /// <param name="theme">主题类型</param>
    void SetTheme(ThemeType theme);
    
    /// <summary>
    /// 切换主题（在亮色和暗色之间切换）
    /// </summary>
    void ToggleTheme();
    
    /// <summary>
    /// 根据系统设置自动设置主题
    /// </summary>
    void SetThemeFromSystem();
    
    /// <summary>
    /// 初始化主题服务
    /// </summary>
    void Initialize();
}

/// <summary>
/// 主题类型枚举
/// </summary>
public enum ThemeType
{
    /// <summary>
    /// 亮色主题
    /// </summary>
    Light,
    
    /// <summary>
    /// 暗色主题
    /// </summary>
    Dark,
    
    /// <summary>
    /// 跟随系统
    /// </summary>
    System
}

/// <summary>
/// 主题变更事件参数
/// </summary>
public class ThemeChangedEventArgs : EventArgs
{
    /// <summary>
    /// 新主题类型
    /// </summary>
    public ThemeType NewTheme { get; }
    
    /// <summary>
    /// 旧主题类型
    /// </summary>
    public ThemeType OldTheme { get; }
    
    public ThemeChangedEventArgs(ThemeType newTheme, ThemeType oldTheme)
    {
        NewTheme = newTheme;
        OldTheme = oldTheme;
    }
}
