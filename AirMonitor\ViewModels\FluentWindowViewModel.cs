using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows;

namespace AirMonitor.ViewModels;

/// <summary>
/// FluentWindow 的视图模型
/// 演示 Community Toolkit.Mvvm 的使用
/// </summary>
public partial class FluentWindowViewModel : ObservableObject
{
    #region 属性

    /// <summary>
    /// 窗体标题
    /// </summary>
    [ObservableProperty]
    private string _windowTitle = "Fluent Design 自定义窗体";

    /// <summary>
    /// 当前主题名称
    /// </summary>
    [ObservableProperty]
    private string _currentTheme = "亮色主题";

    /// <summary>
    /// 是否为暗色主题
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(ThemeToggleText))]
    private bool _isDarkTheme = false;

    /// <summary>
    /// 示例文本内容
    /// </summary>
    [ObservableProperty]
    private string _sampleText = "这是一个示例文本，用于演示字体样式和主题切换效果。";

    /// <summary>
    /// 计数器值
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CounterDisplayText))]
    private int _counter = 0;

    /// <summary>
    /// 是否启用功能
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(ExecuteActionCommand))]
    private bool _isFeatureEnabled = true;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string _statusMessage = "就绪";

    /// <summary>
    /// 状态消息类型
    /// </summary>
    [ObservableProperty]
    private StatusMessageType _statusMessageType = StatusMessageType.Info;

    /// <summary>
    /// 示例项目集合
    /// </summary>
    public ObservableCollection<SampleItem> SampleItems { get; } = new();

    #endregion

    #region 计算属性

    /// <summary>
    /// 主题切换按钮文本
    /// </summary>
    public string ThemeToggleText => IsDarkTheme ? "切换到亮色主题" : "切换到暗色主题";

    /// <summary>
    /// 计数器显示文本
    /// </summary>
    public string CounterDisplayText => $"计数器: {Counter}";

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 FluentWindowViewModel 类的新实例
    /// </summary>
    public FluentWindowViewModel()
    {
        InitializeSampleData();
    }

    #endregion

    #region 命令

    /// <summary>
    /// 切换主题命令
    /// </summary>
    [RelayCommand]
    private void ToggleTheme()
    {
        IsDarkTheme = !IsDarkTheme;
        CurrentTheme = IsDarkTheme ? "暗色主题" : "亮色主题";
        
        // 这里可以添加实际的主题切换逻辑
        // 例如：ThemeService.SetTheme(IsDarkTheme ? Theme.Dark : Theme.Light);
        
        SetStatusMessage($"已切换到{CurrentTheme}", StatusMessageType.Success);
    }

    /// <summary>
    /// 增加计数器命令
    /// </summary>
    [RelayCommand]
    private void IncrementCounter()
    {
        Counter++;
        SetStatusMessage($"计数器已增加到 {Counter}", StatusMessageType.Info);
    }

    /// <summary>
    /// 减少计数器命令
    /// </summary>
    [RelayCommand]
    private void DecrementCounter()
    {
        if (Counter > 0)
        {
            Counter--;
            SetStatusMessage($"计数器已减少到 {Counter}", StatusMessageType.Info);
        }
        else
        {
            SetStatusMessage("计数器不能小于0", StatusMessageType.Warning);
        }
    }

    /// <summary>
    /// 重置计数器命令
    /// </summary>
    [RelayCommand]
    private void ResetCounter()
    {
        Counter = 0;
        SetStatusMessage("计数器已重置", StatusMessageType.Info);
    }

    /// <summary>
    /// 执行操作命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanExecuteAction))]
    private async Task ExecuteActionAsync()
    {
        SetStatusMessage("正在执行操作...", StatusMessageType.Info);
        
        // 模拟异步操作
        await Task.Delay(2000);
        
        SetStatusMessage("操作执行完成", StatusMessageType.Success);
    }

    /// <summary>
    /// 判断是否可以执行操作
    /// </summary>
    private bool CanExecuteAction() => IsFeatureEnabled;

    /// <summary>
    /// 切换功能状态命令
    /// </summary>
    [RelayCommand]
    private void ToggleFeature()
    {
        IsFeatureEnabled = !IsFeatureEnabled;
        var status = IsFeatureEnabled ? "已启用" : "已禁用";
        SetStatusMessage($"功能{status}", StatusMessageType.Info);
    }

    /// <summary>
    /// 添加示例项目命令
    /// </summary>
    [RelayCommand]
    private void AddSampleItem()
    {
        var newItem = new SampleItem
        {
            Id = SampleItems.Count + 1,
            Name = $"项目 {SampleItems.Count + 1}",
            Description = $"这是第 {SampleItems.Count + 1} 个示例项目",
            CreatedTime = DateTime.Now
        };
        
        SampleItems.Add(newItem);
        SetStatusMessage($"已添加项目: {newItem.Name}", StatusMessageType.Success);
    }

    /// <summary>
    /// 删除示例项目命令
    /// </summary>
    [RelayCommand]
    private void RemoveSampleItem(SampleItem? item)
    {
        if (item != null && SampleItems.Contains(item))
        {
            SampleItems.Remove(item);
            SetStatusMessage($"已删除项目: {item.Name}", StatusMessageType.Info);
        }
    }

    /// <summary>
    /// 清空示例项目命令
    /// </summary>
    [RelayCommand]
    private void ClearSampleItems()
    {
        var count = SampleItems.Count;
        SampleItems.Clear();
        SetStatusMessage($"已清空 {count} 个项目", StatusMessageType.Info);
    }

    /// <summary>
    /// 显示关于对话框命令
    /// </summary>
    [RelayCommand]
    private void ShowAbout()
    {
        var message = "Windows 11 Fluent Design 自定义窗体\n\n" +
                     "技术特性:\n" +
                     "• .NET 9.0\n" +
                     "• WPF\n" +
                     "• Community Toolkit.Mvvm\n" +
                     "• Windows 11 Fluent Design\n" +
                     "• WCAG 2.1 AA 无障碍访问\n\n" +
                     "版本: 1.0.0";
        
        MessageBox.Show(message, "关于", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化示例数据
    /// </summary>
    private void InitializeSampleData()
    {
        SampleItems.Add(new SampleItem
        {
            Id = 1,
            Name = "示例项目 1",
            Description = "这是第一个示例项目",
            CreatedTime = DateTime.Now.AddDays(-2)
        });

        SampleItems.Add(new SampleItem
        {
            Id = 2,
            Name = "示例项目 2",
            Description = "这是第二个示例项目",
            CreatedTime = DateTime.Now.AddDays(-1)
        });

        SampleItems.Add(new SampleItem
        {
            Id = 3,
            Name = "示例项目 3",
            Description = "这是第三个示例项目",
            CreatedTime = DateTime.Now
        });
    }

    /// <summary>
    /// 设置状态消息
    /// </summary>
    private void SetStatusMessage(string message, StatusMessageType type)
    {
        StatusMessage = message;
        StatusMessageType = type;
    }

    #endregion
}

/// <summary>
/// 示例项目数据模型
/// </summary>
public partial class SampleItem : ObservableObject
{
    [ObservableProperty]
    private int _id;

    [ObservableProperty]
    private string _name = string.Empty;

    [ObservableProperty]
    private string _description = string.Empty;

    [ObservableProperty]
    private DateTime _createdTime;
}

/// <summary>
/// 状态消息类型枚举
/// </summary>
public enum StatusMessageType
{
    Info,
    Success,
    Warning,
    Error
}
