{"Version": 1, "WorkspaceRootPath": "D:\\项目\\00 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\styles\\windowstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\styles\\windowstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\App.xaml", "RelativeDocumentMoniker": "AirMonitor\\App.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\App.xaml", "RelativeToolTip": "AirMonitor\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T13:39:43.298Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml.cs", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml.cs*", "RelativeToolTip": "AirMonitor\\MainWindow.xaml.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T13:25:39.296Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T13:21:40.563Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "WindowStyles.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Styles\\WindowStyles.xaml", "RelativeDocumentMoniker": "AirMonitor\\Styles\\WindowStyles.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Styles\\WindowStyles.xaml", "RelativeToolTip": "AirMonitor\\Styles\\WindowStyles.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T13:49:16.256Z", "EditorCaption": ""}]}]}]}