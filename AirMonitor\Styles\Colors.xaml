<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Fluent Design Color System
         符合Windows 11 Fluent Design规范的色彩系统
         ======================================== -->

    <!-- 主色调 (Primary Colors) -->
    <!-- 品牌主色 - 蓝色系 -->
    <Color x:Key="SystemAccentColorPrimary">#0078D4</Color>
    <Color x:Key="SystemAccentColorSecondary">#106EBE</Color>
    <Color x:Key="SystemAccentColorTertiary">#005A9E</Color>
    
    <!-- 主色调透明度变体 -->
    <Color x:Key="SystemAccentColorLight1">#E6F3FF</Color>
    <Color x:Key="SystemAccentColorLight2">#CCE7FF</Color>
    <Color x:Key="SystemAccentColorLight3">#99CFFF</Color>
    <Color x:Key="SystemAccentColorDark1">#004578</Color>
    <Color x:Key="SystemAccentColorDark2">#003A63</Color>
    <Color x:Key="SystemAccentColorDark3">#002F4E</Color>

    <!-- 中性色 (Neutral Colors) -->
    <!-- 背景色系 -->
    <Color x:Key="SystemBaseColorHigh">#FFFFFF</Color>
    <Color x:Key="SystemBaseColorMediumHigh">#F9F9F9</Color>
    <Color x:Key="SystemBaseColorMedium">#F3F3F3</Color>
    <Color x:Key="SystemBaseColorMediumLow">#EEEEEE</Color>
    <Color x:Key="SystemBaseColorLow">#E6E6E6</Color>
    
    <!-- 文本色系 -->
    <Color x:Key="SystemBaseForegroundColorHigh">#000000</Color>
    <Color x:Key="SystemBaseForegroundColorMediumHigh">#1F1F1F</Color>
    <Color x:Key="SystemBaseForegroundColorMedium">#666666</Color>
    <Color x:Key="SystemBaseForegroundColorMediumLow">#999999</Color>
    <Color x:Key="SystemBaseForegroundColorLow">#CCCCCC</Color>
    
    <!-- 边框色系 -->
    <Color x:Key="SystemBorderColorHigh">#CCCCCC</Color>
    <Color x:Key="SystemBorderColorMedium">#E0E0E0</Color>
    <Color x:Key="SystemBorderColorLow">#F0F0F0</Color>

    <!-- 语义色 (Semantic Colors) -->
    <!-- 成功色 -->
    <Color x:Key="SystemSuccessColor">#107C10</Color>
    <Color x:Key="SystemSuccessColorLight">#E6F7E6</Color>
    <Color x:Key="SystemSuccessColorDark">#0D5A0D</Color>
    
    <!-- 警告色 -->
    <Color x:Key="SystemWarningColor">#FF8C00</Color>
    <Color x:Key="SystemWarningColorLight">#FFF4E6</Color>
    <Color x:Key="SystemWarningColorDark">#CC7000</Color>
    
    <!-- 错误色 -->
    <Color x:Key="SystemErrorColor">#D13438</Color>
    <Color x:Key="SystemErrorColorLight">#FDEAEA</Color>
    <Color x:Key="SystemErrorColorDark">#A72629</Color>
    
    <!-- 信息色 -->
    <Color x:Key="SystemInfoColor">#0078D4</Color>
    <Color x:Key="SystemInfoColorLight">#E6F3FF</Color>
    <Color x:Key="SystemInfoColorDark">#005A9E</Color>

    <!-- 透明度级别定义 -->
    <!-- 10% 透明度 -->
    <Color x:Key="SystemAccentColor10">#1A0078D4</Color>
    <Color x:Key="SystemBaseColor10">#1A000000</Color>
    
    <!-- 20% 透明度 -->
    <Color x:Key="SystemAccentColor20">#330078D4</Color>
    <Color x:Key="SystemBaseColor20">#33000000</Color>
    
    <!-- 30% 透明度 -->
    <Color x:Key="SystemAccentColor30">#4D0078D4</Color>
    <Color x:Key="SystemBaseColor30">#4D000000</Color>
    
    <!-- 40% 透明度 -->
    <Color x:Key="SystemAccentColor40">#660078D4</Color>
    <Color x:Key="SystemBaseColor40">#66000000</Color>
    
    <!-- 50% 透明度 -->
    <Color x:Key="SystemAccentColor50">#800078D4</Color>
    <Color x:Key="SystemBaseColor50">#80000000</Color>
    
    <!-- 60% 透明度 -->
    <Color x:Key="SystemAccentColor60">#990078D4</Color>
    <Color x:Key="SystemBaseColor60">#99000000</Color>
    
    <!-- 70% 透明度 -->
    <Color x:Key="SystemAccentColor70">#B30078D4</Color>
    <Color x:Key="SystemBaseColor70">#B3000000</Color>
    
    <!-- 80% 透明度 -->
    <Color x:Key="SystemAccentColor80">#CC0078D4</Color>
    <Color x:Key="SystemBaseColor80">#CC000000</Color>
    
    <!-- 90% 透明度 -->
    <Color x:Key="SystemAccentColor90">#E60078D4</Color>
    <Color x:Key="SystemBaseColor90">#E6000000</Color>

</ResourceDictionary>
