<local:FluentWindow x:Class="AirMonitor.Views.TestFluentWindow"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                   xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:local="clr-namespace:AirMonitor.Controls"
                   mc:Ignorable="d"
                   Title="测试 Fluent Window"
                   Width="800"
                   Height="600"
                   MinWidth="400"
                   MinHeight="300"
                   WindowStartupLocation="CenterScreen">

    <!-- 窗体内容 -->
    <Grid Background="{DynamicResource ApplicationBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 内容区域 -->
        <Border Grid.Row="1" 
                Background="{DynamicResource ApplicationBackgroundBrush}"
                Padding="24">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    <!-- 标题 -->
                    <TextBlock Text="Windows 11 Fluent Design 自定义窗体测试"
                              Style="{StaticResource TitleLargeTextStyle}"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,24"/>

                    <!-- 功能说明 -->
                    <Border Background="{DynamicResource CardBackgroundBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="1"
                           CornerRadius="8"
                           Padding="16"
                           Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="功能特性"
                                      Style="{StaticResource SubtitleMediumTextStyle}"
                                      Margin="0,0,0,12"/>
                            
                            <TextBlock Style="{StaticResource BodyMediumTextStyle}">
                                <Run Text="✓ 符合 Windows 11 Fluent Design 设计规范"/>
                                <LineBreak/>
                                <Run Text="✓ 8px 圆角设计，现代化视觉效果"/>
                                <LineBreak/>
                                <Run Text="✓ 完整的明暗主题支持"/>
                                <LineBreak/>
                                <Run Text="✓ WCAG 2.1 AA 无障碍访问标准"/>
                                <LineBreak/>
                                <Run Text="✓ 自定义标题栏和窗体控制按钮"/>
                                <LineBreak/>
                                <Run Text="✓ 支持窗体拖拽和大小调整"/>
                                <LineBreak/>
                                <Run Text="✓ 键盘导航和高DPI支持"/>
                                <LineBreak/>
                                <Run Text="✓ 基于 .NET 9 和 Community Toolkit.Mvvm"/>
                            </TextBlock>
                        </StackPanel>
                    </Border>

                    <!-- 交互演示 -->
                    <Border Background="{DynamicResource CardBackgroundBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="1"
                           CornerRadius="8"
                           Padding="16"
                           Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="交互演示"
                                      Style="{StaticResource SubtitleMediumTextStyle}"
                                      Margin="0,0,0,12"/>
                            
                            <UniformGrid Columns="3" 
                                        HorizontalAlignment="Left">
                                <Button Content="主要按钮"
                                       Margin="0,0,8,8"
                                       Padding="16,8"
                                       Background="{DynamicResource SystemAccentBrush}"
                                       Foreground="White"
                                       BorderThickness="0"
                                       />
                                
                                <Button Content="次要按钮"
                                       Margin="0,0,8,8"
                                       Padding="16,8"
                                       Background="{DynamicResource ControlFillColorDefaultBrush}"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                       BorderThickness="1"
                                       CornerRadius="4"/>

                                <Button Content="测试拖拽"
                                       Margin="0,0,8,8"
                                       Padding="16,8"
                                       Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                       BorderThickness="1"
                                       CornerRadius="4"
                                       ToolTip="点击标题栏可以拖拽窗体"/>
                            </UniformGrid>
                        </StackPanel>
                    </Border>

                    <!-- 文本样式演示 -->
                    <Border Background="{DynamicResource CardBackgroundBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="1"
                           CornerRadius="8"
                           Padding="16"
                           Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="字体层级演示"
                                      Style="{StaticResource SubtitleMediumTextStyle}"
                                      Margin="0,0,0,8"/>
                            
                            <TextBlock Text="Title Large - 页面标题"
                                      Style="{StaticResource TitleLargeTextStyle}"
                                      Margin="0,0,0,4"/>
                            
                            <TextBlock Text="Subtitle Medium - 副标题"
                                      Style="{StaticResource SubtitleMediumTextStyle}"
                                      Margin="0,0,0,4"/>
                            
                            <TextBlock Text="Body Medium - 正文内容，这是标准的正文字体大小，适用于大部分内容展示。"
                                      Style="{StaticResource BodyMediumTextStyle}"
                                      Margin="0,0,0,4"/>
                            
                            <TextBlock Text="Caption Large - 辅助信息和标签"
                                      Style="{StaticResource CaptionLargeTextStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 状态演示 -->
                    <Border Background="{DynamicResource CardBackgroundBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="1"
                           CornerRadius="8"
                           Padding="16">
                        <StackPanel>
                            <TextBlock Text="状态颜色演示"
                                      Style="{StaticResource SubtitleMediumTextStyle}"
                                      Margin="0,0,0,8"/>
                            
                            <TextBlock Text="✓ 成功状态信息"
                                      Style="{StaticResource SuccessTextStyle}"
                                      Margin="0,0,0,4"/>
                            
                            <TextBlock Text="⚠ 警告状态信息"
                                      Style="{StaticResource WarningTextStyle}"
                                      Margin="0,0,0,4"/>
                            
                            <TextBlock Text="✗ 错误状态信息"
                                      Style="{StaticResource ErrorTextStyle}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</local:FluentWindow>
