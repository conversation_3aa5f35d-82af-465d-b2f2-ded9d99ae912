<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- ========================================
         Fluent Design Typography System
         符合Windows 11 Fluent Design规范的字体系统
         支持中英文混排和可访问性要求
         ======================================== -->

    <!-- 字体族定义 (Font Family Definitions) -->
    <!-- 中文优先字体族 - 适用于界面文本 -->
    <FontFamily x:Key="SystemFontFamily">Microsoft YaHei UI, Microsoft YaHei, Segoe UI, Tahoma, Arial, sans-serif</FontFamily>
    
    <!-- 英文优先字体族 - 适用于纯英文内容 -->
    <FontFamily x:Key="SystemFontFamilyEnglish">Segoe UI, Microsoft YaHei UI, Microsoft YaHei, Tahoma, Arial, sans-serif</FontFamily>
    
    <!-- 等宽字体族 - 适用于代码和数据显示 -->
    <FontFamily x:Key="SystemFontFamilyMonospace">Cascadia Code, Consolas, Courier New, monospace</FontFamily>
    
    <!-- 数字字体族 - 适用于数字显示，确保数字对齐 -->
    <FontFamily x:Key="SystemFontFamilyNumeric">Segoe UI, Microsoft YaHei UI, Tahoma, Arial, sans-serif</FontFamily>

    <!-- ========================================
         字体大小层级系统 (Typography Scale)
         基于8pt网格系统，确保视觉一致性
         ======================================== -->

    <!-- Display 层级 - 用于大标题和重要展示 -->
    <sys:Double x:Key="FontSizeDisplay">68</sys:Double>        <!-- 68pt -->
    <sys:Double x:Key="FontSizeDisplayLarge">57</sys:Double>   <!-- 57pt -->
    <sys:Double x:Key="FontSizeDisplayMedium">45</sys:Double>  <!-- 45pt -->
    <sys:Double x:Key="FontSizeDisplaySmall">36</sys:Double>   <!-- 36pt -->

    <!-- Title 层级 - 用于页面标题和重要标题 -->
    <sys:Double x:Key="FontSizeTitleLarge">28</sys:Double>     <!-- 28pt -->
    <sys:Double x:Key="FontSizeTitleMedium">20</sys:Double>    <!-- 20pt -->
    <sys:Double x:Key="FontSizeTitleSmall">16</sys:Double>     <!-- 16pt -->

    <!-- Subtitle 层级 - 用于副标题和分组标题 -->
    <sys:Double x:Key="FontSizeSubtitleLarge">20</sys:Double>  <!-- 20pt -->
    <sys:Double x:Key="FontSizeSubtitleMedium">16</sys:Double> <!-- 16pt -->
    <sys:Double x:Key="FontSizeSubtitleSmall">14</sys:Double>  <!-- 14pt -->

    <!-- Body 层级 - 用于正文内容 -->
    <sys:Double x:Key="FontSizeBodyLarge">16</sys:Double>      <!-- 16pt -->
    <sys:Double x:Key="FontSizeBodyMedium">14</sys:Double>     <!-- 14pt -->
    <sys:Double x:Key="FontSizeBodySmall">12</sys:Double>      <!-- 12pt -->
    <sys:Double x:Key="FontSizeBodyStrong">14</sys:Double>     <!-- 14pt -->

    <!-- Caption 层级 - 用于辅助信息和标签 -->
    <sys:Double x:Key="FontSizeCaptionLarge">12</sys:Double>   <!-- 12pt -->
    <sys:Double x:Key="FontSizeCaptionMedium">11</sys:Double>  <!-- 11pt -->
    <sys:Double x:Key="FontSizeCaptionSmall">10</sys:Double>   <!-- 10pt -->

    <!-- ========================================
         行高定义 (Line Height)
         基于字体大小的1.2-1.5倍，确保良好的可读性
         ======================================== -->

    <!-- Display 行高 -->
    <sys:Double x:Key="LineHeightDisplay">82</sys:Double>        <!-- 68 * 1.2 -->
    <sys:Double x:Key="LineHeightDisplayLarge">68</sys:Double>   <!-- 57 * 1.2 -->
    <sys:Double x:Key="LineHeightDisplayMedium">54</sys:Double>  <!-- 45 * 1.2 -->
    <sys:Double x:Key="LineHeightDisplaySmall">43</sys:Double>   <!-- 36 * 1.2 -->

    <!-- Title 行高 -->
    <sys:Double x:Key="LineHeightTitleLarge">36</sys:Double>     <!-- 28 * 1.3 -->
    <sys:Double x:Key="LineHeightTitleMedium">26</sys:Double>    <!-- 20 * 1.3 -->
    <sys:Double x:Key="LineHeightTitleSmall">21</sys:Double>     <!-- 16 * 1.3 -->

    <!-- Subtitle 行高 -->
    <sys:Double x:Key="LineHeightSubtitleLarge">26</sys:Double>  <!-- 20 * 1.3 -->
    <sys:Double x:Key="LineHeightSubtitleMedium">21</sys:Double> <!-- 16 * 1.3 -->
    <sys:Double x:Key="LineHeightSubtitleSmall">18</sys:Double>  <!-- 14 * 1.3 -->

    <!-- Body 行高 -->
    <sys:Double x:Key="LineHeightBodyLarge">24</sys:Double>      <!-- 16 * 1.5 -->
    <sys:Double x:Key="LineHeightBodyMedium">21</sys:Double>     <!-- 14 * 1.5 -->
    <sys:Double x:Key="LineHeightBodySmall">18</sys:Double>      <!-- 12 * 1.5 -->
    <sys:Double x:Key="LineHeightBodyStrong">21</sys:Double>     <!-- 14 * 1.5 -->

    <!-- Caption 行高 -->
    <sys:Double x:Key="LineHeightCaptionLarge">16</sys:Double>   <!-- 12 * 1.3 -->
    <sys:Double x:Key="LineHeightCaptionMedium">14</sys:Double>  <!-- 11 * 1.3 -->
    <sys:Double x:Key="LineHeightCaptionSmall">13</sys:Double>   <!-- 10 * 1.3 -->

    <!-- ========================================
         字重定义 (Font Weight)
         符合Fluent Design的字重规范
         ======================================== -->

    <!-- 标准字重 -->
    <FontWeight x:Key="FontWeightLight">Light</FontWeight>           <!-- 300 -->
    <FontWeight x:Key="FontWeightNormal">Normal</FontWeight>         <!-- 400 -->
    <FontWeight x:Key="FontWeightMedium">Medium</FontWeight>         <!-- 500 -->
    <FontWeight x:Key="FontWeightSemiBold">SemiBold</FontWeight>     <!-- 600 -->
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>             <!-- 700 -->

    <!-- ========================================
         完整字体样式定义 (Complete Typography Styles)
         包含字体族、大小、行高、字重的完整样式
         ======================================== -->

    <!-- Display 样式 -->
    <Style x:Key="DisplayTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplay}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplay}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="DisplayLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplayLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplayLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="DisplayMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplayMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplayMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="DisplaySmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeDisplaySmall}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightDisplaySmall}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- Title 样式 -->
    <Style x:Key="TitleLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitleLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitleLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="TitleMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitleMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitleMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="TitleSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitleSmall}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitleSmall}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- Subtitle 样式 -->
    <Style x:Key="SubtitleLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeSubtitleLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightSubtitleLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="SubtitleMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeSubtitleMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightSubtitleMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="SubtitleSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeSubtitleSmall}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightSubtitleSmall}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- Body 样式 -->
    <Style x:Key="BodyLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="BodyMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="BodySmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodySmall}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodySmall}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="BodyStrongTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyStrong}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyStrong}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- Caption 样式 -->
    <Style x:Key="CaptionLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaptionLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="CaptionMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaptionMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style x:Key="CaptionSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionSmall}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaptionSmall}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- ========================================
         特殊用途字体样式 (Special Purpose Styles)
         ======================================== -->

    <!-- 等宽字体样式 - 用于代码显示 -->
    <Style x:Key="CodeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamilyMonospace}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 数字显示样式 - 确保数字对齐 -->
    <Style x:Key="NumericTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamilyNumeric}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Right"/>
    </Style>

    <!-- 大数字显示样式 - 用于仪表盘等场景 -->
    <Style x:Key="NumericLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamilyNumeric}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitleLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightTitleLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Right"/>
    </Style>

    <!-- 链接文本样式 -->
    <Style x:Key="LinkTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemAccentBrush}"/>
        <Setter Property="TextDecorations" Value="Underline"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <!-- 错误文本样式 -->
    <Style x:Key="ErrorTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaptionLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorCriticalBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 成功文本样式 -->
    <Style x:Key="SuccessTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaptionLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 警告文本样式 -->
    <Style x:Key="WarningTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightCaptionLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorWarningBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- ========================================
         可访问性增强样式 (Accessibility Enhanced Styles)
         符合WCAG 2.1 AA标准
         ======================================== -->

    <!-- 高对比度文本样式 -->
    <Style x:Key="HighContrastTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyLarge}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 大字体样式 - 用于可访问性 -->
    <Style x:Key="LargeFontTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="28"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- ========================================
         输入控件字体样式 (Input Control Typography)
         适用于TextBox、ComboBox等输入控件
         ======================================== -->

    <!-- 输入框文本样式 -->
    <Style x:Key="InputTextStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
    </Style>

    <!-- 占位符文本样式 -->
    <Style x:Key="PlaceholderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorTertiaryBrush}"/>
        <Setter Property="FontStyle" Value="Italic"/>
    </Style>

    <!-- 标签文本样式 -->
    <Style x:Key="LabelTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource LineHeightBodyMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>

    <!-- 必填字段标签样式 -->
    <Style x:Key="RequiredLabelTextStyle" TargetType="TextBlock" BasedOn="{StaticResource LabelTextStyle}">
        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorCriticalBrush}"/>
    </Style>

    <!-- ========================================
         按钮字体样式 (Button Typography)
         ======================================== -->

    <!-- 主要按钮文本样式 -->
    <Style x:Key="PrimaryButtonTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorInverseBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
    </Style>

    <!-- 次要按钮文本样式 -->
    <Style x:Key="SecondaryButtonTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
    </Style>

    <!-- 图标按钮文本样式 -->
    <Style x:Key="IconButtonTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeCaptionLarge}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,4,0,0"/>
    </Style>

</ResourceDictionary>

