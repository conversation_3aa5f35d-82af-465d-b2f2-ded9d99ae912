<Window
    x:Class="AirMonitor.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AirMonitor.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:AirMonitor"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Width="1200"
    Height="800"
    MinWidth="800"
    MinHeight="600"
    Style="{DynamicResource FluentCustomWindowStyle}"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Grid Background="{DynamicResource ApplicationBackgroundBrush}">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock
                HorizontalAlignment="Center"
                FontSize="32"
                Foreground="Red"
                Text="Hello FluentWindow!"
                Margin="0,0,0,20" />

            <Button Content="打开测试窗体"
                    Name="OpenTestWindowButton"
                    Click="OpenTestWindowButton_Click"
                    Padding="20,10"
                    Background="{DynamicResource SystemAccentBrush}"
                    Foreground="White"
                    BorderThickness="0"
                    
                    FontSize="14"/>
        </StackPanel>
    </Grid>
</Window>
