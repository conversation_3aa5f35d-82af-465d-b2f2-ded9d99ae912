<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Windows 11 Fluent Design Custom Window Styles
         符合Windows 11 Fluent Design规范的自定义窗体样式
         支持明暗主题切换和WCAG 2.1 AA无障碍访问标准
         ======================================== -->

    <!-- 合并基础资源 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
        <ResourceDictionary Source="Fonts.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ========================================
         窗体按钮样式 (Window Button Styles)
         ======================================== -->

    <!-- 窗体按钮基础样式 -->
    <Style x:Key="WindowButtonBaseStyle" TargetType="Button">
        <Setter Property="Width" Value="46"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Focusable" Value="True"/>
        <Setter Property="IsTabStop" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="0">
                        <ContentPresenter x:Name="ContentPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Focusable="False"
                                        RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" 
                                    Value="{DynamicResource ControlFillColorDefaultHoverBrush}"/>
                        </Trigger>
                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" 
                                    Value="{DynamicResource ControlFillColorDefaultPressedBrush}"/>
                        </Trigger>
                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" 
                                    Value="{DynamicResource FocusStrokeColorOuterBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 最小化按钮样式 -->
    <Style x:Key="MinimizeButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowButtonBaseStyle}">
        <Setter Property="Content" Value="&#xE921;"/>
        <Setter Property="ToolTip" Value="最小化"/>
        <Setter Property="AutomationProperties.Name" Value="最小化"/>
        <Setter Property="AutomationProperties.HelpText" Value="最小化窗口"/>
    </Style>

    <!-- 最大化按钮样式 -->
    <Style x:Key="MaximizeButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowButtonBaseStyle}">
        <Setter Property="Content" Value="&#xE922;"/>
        <Setter Property="ToolTip" Value="最大化"/>
        <Setter Property="AutomationProperties.Name" Value="最大化"/>
        <Setter Property="AutomationProperties.HelpText" Value="最大化窗口"/>
    </Style>

    <!-- 还原按钮样式 -->
    <Style x:Key="RestoreButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowButtonBaseStyle}">
        <Setter Property="Content" Value="&#xE923;"/>
        <Setter Property="ToolTip" Value="向下还原"/>
        <Setter Property="AutomationProperties.Name" Value="向下还原"/>
        <Setter Property="AutomationProperties.HelpText" Value="将窗口还原到正常大小"/>
    </Style>

    <!-- 关闭按钮样式 -->
    <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowButtonBaseStyle}">
        <Setter Property="Content" Value="&#xE8BB;"/>
        <Setter Property="ToolTip" Value="关闭"/>
        <Setter Property="AutomationProperties.Name" Value="关闭"/>
        <Setter Property="AutomationProperties.HelpText" Value="关闭窗口"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="0">
                        <ContentPresenter x:Name="ContentPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Focusable="False"
                                        RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 - 关闭按钮使用红色 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" 
                                    Value="{DynamicResource SystemFillColorCriticalBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" 
                                    Value="#C42B1C"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" 
                                    Value="{DynamicResource FocusStrokeColorOuterBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ========================================
         标题栏样式 (Title Bar Styles)
         ======================================== -->

    <!-- 标题栏容器样式 -->
    <Style x:Key="TitleBarStyle" TargetType="Grid">
        <Setter Property="Height" Value="32"/>
        <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}"/>
    </Style>

    <!-- 窗体标题文本样式 -->
    <Style x:Key="WindowTitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource CaptionLargeTextStyle}">
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="12,0,0,0"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- 窗体图标样式 -->
    <Style x:Key="WindowIconStyle" TargetType="Image">
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Margin" Value="8,0,4,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="RenderOptions.BitmapScalingMode" Value="HighQuality"/>
    </Style>

    <!-- ========================================
         自定义窗体主样式 (Custom Window Main Style)
         ======================================== -->

    <!-- 自定义窗体样式 -->
    <Style x:Key="FluentCustomWindowStyle" TargetType="Window">
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="ResizeMode" Value="CanResize"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="MinWidth" Value="320"/>
        <Setter Property="MinHeight" Value="240"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Window">
                    <!-- 主窗体容器 -->
                    <Border x:Name="WindowBorder"
                            Background="{DynamicResource ApplicationBackgroundBrush}"
                            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                            BorderThickness="1"
                            CornerRadius="8">
                        <Grid>
                            <Grid.RowDefinitions>
                                <!-- 标题栏 -->
                                <RowDefinition Height="32"/>
                                <!-- 内容区域 -->
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 标题栏 -->
                            <Border x:Name="TitleBarBorder"
                                    Grid.Row="0"
                                    Background="{DynamicResource ApplicationBackgroundBrush}"
                                    CornerRadius="8,8,0,0"
                                    Height="32">
                                <Grid x:Name="TitleBar">
                                    <Grid.ColumnDefinitions>
                                        <!-- 图标和标题 -->
                                        <ColumnDefinition Width="*"/>
                                        <!-- 窗体按钮 -->
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 左侧：图标和标题 -->
                                    <StackPanel Grid.Column="0"
                                              Orientation="Horizontal"
                                              VerticalAlignment="Center">
                                        <!-- 窗体图标 -->
                                        <Image x:Name="WindowIcon"
                                               Style="{StaticResource WindowIconStyle}"
                                               Source="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Icon}"/>

                                        <!-- 窗体标题 -->
                                        <TextBlock x:Name="WindowTitle"
                                                 Style="{StaticResource WindowTitleTextStyle}"
                                                 Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Title}"/>
                                    </StackPanel>

                                    <!-- 右侧：窗体控制按钮 -->
                                    <StackPanel Grid.Column="1"
                                              Orientation="Horizontal"
                                              VerticalAlignment="Center">
                                        <!-- 最小化按钮 -->
                                        <Button x:Name="MinimizeButton"
                                                Style="{StaticResource MinimizeButtonStyle}"/>

                                        <!-- 最大化/还原按钮 -->
                                        <Button x:Name="MaximizeRestoreButton"
                                                Style="{StaticResource MaximizeButtonStyle}"/>

                                        <!-- 关闭按钮 -->
                                        <Button x:Name="CloseButton"
                                                Style="{StaticResource CloseButtonStyle}"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- 内容区域 -->
                            <Border x:Name="ContentBorder"
                                    Grid.Row="1"
                                    Background="{DynamicResource ApplicationBackgroundBrush}"
                                    CornerRadius="0,0,7,7"
                                    ClipToBounds="True">
                                <ContentPresenter x:Name="ContentPresenter"
                                                Content="{TemplateBinding Content}"
                                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>

                            <!-- 调整大小边框 -->
                            <Grid x:Name="ResizeGrid">
                                <!-- 顶部调整 -->
                                <Rectangle x:Name="TopResize"
                                         Fill="Transparent"
                                         Height="4"
                                         VerticalAlignment="Top"
                                         Cursor="SizeNS"/>
                                <!-- 底部调整 -->
                                <Rectangle x:Name="BottomResize"
                                         Fill="Transparent"
                                         Height="4"
                                         VerticalAlignment="Bottom"
                                         Cursor="SizeNS"/>
                                <!-- 左侧调整 -->
                                <Rectangle x:Name="LeftResize"
                                         Fill="Transparent"
                                         Width="4"
                                         HorizontalAlignment="Left"
                                         Cursor="SizeWE"/>
                                <!-- 右侧调整 -->
                                <Rectangle x:Name="RightResize"
                                         Fill="Transparent"
                                         Width="4"
                                         HorizontalAlignment="Right"
                                         Cursor="SizeWE"/>
                                <!-- 左上角调整 -->
                                <Rectangle x:Name="TopLeftResize"
                                         Fill="Transparent"
                                         Width="8"
                                         Height="8"
                                         HorizontalAlignment="Left"
                                         VerticalAlignment="Top"
                                         Cursor="SizeNWSE"/>
                                <!-- 右上角调整 -->
                                <Rectangle x:Name="TopRightResize"
                                         Fill="Transparent"
                                         Width="8"
                                         Height="8"
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Top"
                                         Cursor="SizeNESW"/>
                                <!-- 左下角调整 -->
                                <Rectangle x:Name="BottomLeftResize"
                                         Fill="Transparent"
                                         Width="8"
                                         Height="8"
                                         HorizontalAlignment="Left"
                                         VerticalAlignment="Bottom"
                                         Cursor="SizeNESW"/>
                                <!-- 右下角调整 -->
                                <Rectangle x:Name="BottomRightResize"
                                         Fill="Transparent"
                                         Width="8"
                                         Height="8"
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Bottom"
                                         Cursor="SizeNWSE"/>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- 控件模板触发器 -->
                    <ControlTemplate.Triggers>
                        <!-- 窗体状态触发器 -->
                        <Trigger Property="WindowState" Value="Maximized">
                            <Setter TargetName="WindowBorder" Property="CornerRadius" Value="0"/>
                            <Setter TargetName="ContentBorder" Property="CornerRadius" Value="0"/>
                            <Setter TargetName="TitleBarBorder" Property="CornerRadius" Value="0"/>
                            <Setter TargetName="WindowBorder" Property="BorderThickness" Value="0"/>
                            <Setter TargetName="MaximizeRestoreButton" Property="Style"
                                    Value="{StaticResource RestoreButtonStyle}"/>
                            <Setter TargetName="ResizeGrid" Property="Visibility" Value="Collapsed"/>
                        </Trigger>

                        <!-- 窗体激活状态 -->
                        <Trigger Property="IsActive" Value="False">
                            <Setter TargetName="WindowBorder" Property="BorderBrush"
                                    Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
                            <Setter TargetName="WindowTitle" Property="Foreground"
                                    Value="{DynamicResource TextFillColorTertiaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
