<Window x:Class="AirMonitor.Controls.FluentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Style="{StaticResource FluentCustomWindowStyle}"
        Title="Fluent Window"
        Width="800"
        Height="600"
        MinWidth="320"
        MinHeight="240"
        WindowStartupLocation="CenterScreen"
        UseLayoutRounding="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.ClearTypeHint="Enabled"
        SnapsToDevicePixels="True">

    <!-- 窗体资源 -->
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/WindowStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 窗体内容 -->
    <Grid Background="{DynamicResource ApplicationBackgroundBrush}">
        <!-- 示例内容 -->
        <Border Background="{DynamicResource ApplicationBackgroundBrush}"
                Padding="24">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="Hello FluentWindow!"
                          Style="{StaticResource TitleLargeTextStyle}"
                          HorizontalAlignment="Center"
                          Foreground="Red"
                          Margin="0,0,0,20"/>
                
                <TextBlock Text="这是一个符合Windows 11 Fluent Design规范的自定义窗体"
                          Style="{StaticResource BodyMediumTextStyle}"
                          HorizontalAlignment="Center"
                          TextWrapping="Wrap"
                          TextAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
