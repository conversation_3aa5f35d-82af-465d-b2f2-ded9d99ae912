using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;

namespace AirMonitor.Controls;

/// <summary>
/// 符合Windows 11 Fluent Design规范的自定义窗体控件
/// 支持完整的明暗主题切换和WCAG 2.1 AA无障碍访问标准
/// </summary>
public partial class FluentWindow : Window
{
    #region Win32 API 声明

    [DllImport("user32.dll")]
    private static extern IntPtr SendMessage(IntPtr hWnd, int wMsg, IntPtr wParam, IntPtr lParam);

    [DllImport("user32.dll")]
    private static extern bool ReleaseCapture();

    private const int WM_NCLBUTTONDOWN = 0xA1;
    private const int HT_CAPTION = 0x2;
    private const int HT_TOPLEFT = 0xD;
    private const int HT_TOPRIGHT = 0xE;
    private const int HT_BOTTOMLEFT = 0x10;
    private const int HT_BOTTOMRIGHT = 0x11;
    private const int HT_LEFT = 0xA;
    private const int HT_RIGHT = 0xB;
    private const int HT_TOP = 0xC;
    private const int HT_BOTTOM = 0xF;

    #endregion

    #region 依赖属性

    /// <summary>
    /// 是否显示窗体图标
    /// </summary>
    public static readonly DependencyProperty ShowIconProperty =
        DependencyProperty.Register(
            nameof(ShowIcon),
            typeof(bool),
            typeof(FluentWindow),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示最小化按钮
    /// </summary>
    public static readonly DependencyProperty ShowMinimizeButtonProperty =
        DependencyProperty.Register(
            nameof(ShowMinimizeButton),
            typeof(bool),
            typeof(FluentWindow),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示最大化按钮
    /// </summary>
    public static readonly DependencyProperty ShowMaximizeButtonProperty =
        DependencyProperty.Register(
            nameof(ShowMaximizeButton),
            typeof(bool),
            typeof(FluentWindow),
            new PropertyMetadata(true));

    #endregion

    #region 属性

    /// <summary>
    /// 获取或设置是否显示窗体图标
    /// </summary>
    [Category("Fluent Window")]
    [Description("获取或设置是否显示窗体图标")]
    public bool ShowIcon
    {
        get => (bool)GetValue(ShowIconProperty);
        set => SetValue(ShowIconProperty, value);
    }

    /// <summary>
    /// 获取或设置是否显示最小化按钮
    /// </summary>
    [Category("Fluent Window")]
    [Description("获取或设置是否显示最小化按钮")]
    public bool ShowMinimizeButton
    {
        get => (bool)GetValue(ShowMinimizeButtonProperty);
        set => SetValue(ShowMinimizeButtonProperty, value);
    }

    /// <summary>
    /// 获取或设置是否显示最大化按钮
    /// </summary>
    [Category("Fluent Window")]
    [Description("获取或设置是否显示最大化按钮")]
    public bool ShowMaximizeButton
    {
        get => (bool)GetValue(ShowMaximizeButtonProperty);
        set => SetValue(ShowMaximizeButtonProperty, value);
    }

    #endregion

    #region 私有字段

    private Button? _minimizeButton;
    private Button? _maximizeRestoreButton;
    private Button? _closeButton;
    private Grid? _titleBar;
    private bool _isResizing;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 FluentWindow 类的新实例
    /// </summary>
    public FluentWindow()
    {
        InitializeComponent();
        
        // 设置默认属性
        WindowStyle = WindowStyle.None;
        AllowsTransparency = true;
        Background = Brushes.Transparent;
        
        // 订阅事件
        Loaded += OnLoaded;
        StateChanged += OnStateChanged;
        
        // 设置高DPI感知
        SetValue(RenderOptions.ClearTypeHintProperty, ClearTypeHint.Enabled);
        SetValue(TextOptions.TextFormattingModeProperty, TextFormattingMode.Display);
        SetValue(TextOptions.TextRenderingModeProperty, TextRenderingMode.ClearType);
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 重写OnApplyTemplate方法，在模板应用后获取控件引用
    /// </summary>
    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();
        
        // 获取模板中的控件引用
        _minimizeButton = GetTemplateChild("MinimizeButton") as Button;
        _maximizeRestoreButton = GetTemplateChild("MaximizeRestoreButton") as Button;
        _closeButton = GetTemplateChild("CloseButton") as Button;
        _titleBar = GetTemplateChild("TitleBar") as Grid;

        // 绑定按钮事件
        if (_minimizeButton != null)
        {
            _minimizeButton.Click += OnMinimizeClick;
            _minimizeButton.Visibility = ShowMinimizeButton ? Visibility.Visible : Visibility.Collapsed;
        }

        if (_maximizeRestoreButton != null)
        {
            _maximizeRestoreButton.Click += OnMaximizeRestoreClick;
            _maximizeRestoreButton.Visibility = ShowMaximizeButton ? Visibility.Visible : Visibility.Collapsed;
        }

        if (_closeButton != null)
        {
            _closeButton.Click += OnCloseClick;
        }

        if (_titleBar != null)
        {
            _titleBar.MouseLeftButtonDown += OnTitleBarMouseLeftButtonDown;
            _titleBar.MouseDoubleClick += OnTitleBarMouseDoubleClick;
        }

        // 绑定调整大小事件
        BindResizeEvents();
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 窗体加载完成事件处理
    /// </summary>
    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 模板应用在OnApplyTemplate中处理
    }

    /// <summary>
    /// 窗体状态改变事件处理
    /// </summary>
    private void OnStateChanged(object? sender, EventArgs e)
    {
        // 更新最大化/还原按钮的样式
        if (_maximizeRestoreButton != null)
        {
            var style = WindowState == WindowState.Maximized 
                ? FindResource("RestoreButtonStyle") as Style
                : FindResource("MaximizeButtonStyle") as Style;
            
            if (style != null)
            {
                _maximizeRestoreButton.Style = style;
            }
        }
    }

    /// <summary>
    /// 最小化按钮点击事件
    /// </summary>
    private void OnMinimizeClick(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    /// <summary>
    /// 最大化/还原按钮点击事件
    /// </summary>
    private void OnMaximizeRestoreClick(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized 
            ? WindowState.Normal 
            : WindowState.Maximized;
    }

    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    private void OnCloseClick(object sender, RoutedEventArgs e)
    {
        Close();
    }

    /// <summary>
    /// 标题栏鼠标左键按下事件
    /// </summary>
    private void OnTitleBarMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ChangedButton == MouseButton.Left && !_isResizing)
        {
            try
            {
                DragMove();
            }
            catch (InvalidOperationException)
            {
                // 忽略拖动时可能出现的异常
            }
        }
    }

    /// <summary>
    /// 标题栏鼠标双击事件
    /// </summary>
    private void OnTitleBarMouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (ShowMaximizeButton && e.ChangedButton == MouseButton.Left)
        {
            WindowState = WindowState == WindowState.Maximized 
                ? WindowState.Normal 
                : WindowState.Maximized;
        }
    }

    #endregion

    #region 窗体大小调整

    /// <summary>
    /// 绑定调整大小事件
    /// </summary>
    private void BindResizeEvents()
    {
        // 获取调整大小的矩形控件
        var resizeRects = new[]
        {
            ("TopResize", HT_TOP),
            ("BottomResize", HT_BOTTOM),
            ("LeftResize", HT_LEFT),
            ("RightResize", HT_RIGHT),
            ("TopLeftResize", HT_TOPLEFT),
            ("TopRightResize", HT_TOPRIGHT),
            ("BottomLeftResize", HT_BOTTOMLEFT),
            ("BottomRightResize", HT_BOTTOMRIGHT)
        };

        foreach (var (name, hitTest) in resizeRects)
        {
            if (GetTemplateChild(name) is FrameworkElement element)
            {
                element.MouseLeftButtonDown += (s, e) => OnResizeMouseDown(hitTest);
            }
        }
    }

    /// <summary>
    /// 调整大小鼠标按下事件
    /// </summary>
    private void OnResizeMouseDown(int hitTest)
    {
        if (WindowState == WindowState.Maximized) return;

        _isResizing = true;
        
        var hwnd = new WindowInteropHelper(this).Handle;
        ReleaseCapture();
        SendMessage(hwnd, WM_NCLBUTTONDOWN, (IntPtr)hitTest, IntPtr.Zero);
        
        _isResizing = false;
    }

    #endregion
}
