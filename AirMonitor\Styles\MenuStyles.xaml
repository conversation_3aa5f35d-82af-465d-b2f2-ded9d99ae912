<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:AirMonitor.Controls"
                    xmlns:converters="clr-namespace:AirMonitor.Converters">

    <!-- ========================================
         Fluent Design Menu System Styles
         符合Windows 11 Fluent Design规范的菜单样式
         ======================================== -->

    <!-- 转换器资源 -->
    <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    <converters:CollectionCountToBooleanConverter x:Key="CollectionCountToBooleanConverter"/>

    <!-- 菜单栏样式 -->
    <Style TargetType="{x:Type controls:FluentMenuBar}">
        <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Height" Value="{Binding MenuBarHeight, RelativeSource={RelativeSource Self}}"/>
        <Setter Property="Padding" Value="8,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:FluentMenuBar}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}">
                        <ItemsControl ItemsSource="{TemplateBinding MenuItems}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <controls:FluentMenuItem 
                                        Header="{Binding Header}"
                                        Icon="{Binding Icon}"
                                        InputGestureText="{Binding InputGestureText}"
                                        AccessKey="{Binding AccessKey}"
                                        IsSeparator="{Binding IsSeparator}"
                                        IsCheckable="{Binding IsCheckable}"
                                        IsChecked="{Binding IsChecked}"
                                        Items="{Binding Items}"
                                        Command="{Binding Command}"
                                        CommandParameter="{Binding CommandParameter}"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 菜单项样式 -->
    <Style TargetType="{x:Type controls:FluentMenuItem}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource SystemFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeBodyMedium}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Margin" Value="2,0"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:FluentMenuItem}">
                    <!-- 分隔符模板 -->
                    <ControlTemplate.Resources>
                        <ControlTemplate x:Key="SeparatorTemplate" TargetType="{x:Type controls:FluentMenuItem}">
                            <Rectangle Height="1" 
                                       Margin="8,4"
                                       Fill="{DynamicResource DividerStrokeColorDefaultBrush}"/>
                        </ControlTemplate>
                    </ControlTemplate.Resources>

                    <!-- 普通菜单项模板 -->
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="Transparent"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/> <!-- 图标 -->
                                <ColumnDefinition Width="*"/>    <!-- 文本 -->
                                <ColumnDefinition Width="Auto"/> <!-- 快捷键 -->
                                <ColumnDefinition Width="Auto"/> <!-- 子菜单箭头 -->
                            </Grid.ColumnDefinitions>

                            <!-- 选中标记/图标 -->
                            <Grid Grid.Column="0" Width="20" Height="20" Margin="0,0,8,0">
                                <!-- 选中标记 -->
                                <Path x:Name="CheckMark"
                                      Data="M9,16.17L4.83,12L3.41,13.41L9,19L21,7L19.59,5.59L9,16.17Z"
                                      Fill="{DynamicResource SystemAccentBrush}"
                                      Stretch="Uniform"
                                      Width="12" Height="12"
                                      Visibility="Collapsed"/>
                                
                                <!-- 自定义图标 -->
                                <Path x:Name="CustomIcon"
                                      Data="{TemplateBinding Icon}"
                                      Fill="{TemplateBinding Foreground}"
                                      Stretch="Uniform"
                                      Width="16" Height="16"
                                      Visibility="{Binding Icon, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource NullToVisibilityConverter}}"/>
                            </Grid>

                            <!-- 菜单文本 -->
                            <TextBlock x:Name="HeaderText"
                                       Grid.Column="1"
                                       Text="{TemplateBinding Header}"
                                       Foreground="{TemplateBinding Foreground}"
                                       FontFamily="{TemplateBinding FontFamily}"
                                       FontSize="{TemplateBinding FontSize}"
                                       FontWeight="{TemplateBinding FontWeight}"
                                       VerticalAlignment="Center"
                                       TextTrimming="CharacterEllipsis"/>

                            <!-- 快捷键文本 -->
                            <TextBlock x:Name="GestureText"
                                       Grid.Column="2"
                                       Text="{TemplateBinding InputGestureText}"
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                       FontFamily="{TemplateBinding FontFamily}"
                                       FontSize="{StaticResource FontSizeCaptionLarge}"
                                       VerticalAlignment="Center"
                                       Margin="16,0,0,0"
                                       Visibility="{Binding InputGestureText, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource StringToVisibilityConverter}}"/>

                            <!-- 子菜单箭头 -->
                            <Path x:Name="SubMenuArrow"
                                  Grid.Column="3"
                                  Data="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
                                  Fill="{TemplateBinding Foreground}"
                                  Stretch="Uniform"
                                  Width="12" Height="12"
                                  Margin="8,0,0,0"
                                  Visibility="Collapsed"/>

                            <!-- 子菜单弹出框 -->
                            <Popup x:Name="SubMenuPopup"
                                   IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   Placement="Bottom"
                                   PlacementTarget="{Binding RelativeSource={RelativeSource TemplatedParent}}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Fade">
                                <Border Background="{DynamicResource CardBackgroundBrush}"
                                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                        BorderThickness="1"
                                        CornerRadius="8"
                                        Padding="4"
                                        Effect="{StaticResource MenuShadowEffect}">
                                    <ItemsControl ItemsSource="{TemplateBinding Items}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <controls:FluentMenuItem 
                                                    Header="{Binding Header}"
                                                    Icon="{Binding Icon}"
                                                    InputGestureText="{Binding InputGestureText}"
                                                    AccessKey="{Binding AccessKey}"
                                                    IsSeparator="{Binding IsSeparator}"
                                                    IsCheckable="{Binding IsCheckable}"
                                                    IsChecked="{Binding IsChecked}"
                                                    Items="{Binding Items}"
                                                    Command="{Binding Command}"
                                                    CommandParameter="{Binding CommandParameter}"/>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <!-- 分隔符状态 -->
                        <Trigger Property="IsSeparator" Value="True">
                            <Setter Property="Template" Value="{StaticResource SeparatorTemplate}"/>
                        </Trigger>

                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" 
                                    Value="{DynamicResource ControlFillColorDefaultHoverBrush}"/>
                        </Trigger>

                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" 
                                    Value="{DynamicResource ControlFillColorDefaultPressedBrush}"/>
                        </Trigger>

                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" 
                                    Value="{DynamicResource SystemAccentBrush}"/>
                            <Setter TargetName="Border" Property="Background" 
                                    Value="{DynamicResource ControlFillColorDefaultHoverBrush}"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" 
                                    Value="{DynamicResource TextFillColorDisabledBrush}"/>
                            <Setter Property="Cursor" Value="Arrow"/>
                        </Trigger>

                        <!-- 选中状态 -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                        </Trigger>

                        <!-- 有子菜单 -->
                        <DataTrigger Binding="{Binding HasItems, RelativeSource={RelativeSource Self}}" Value="True">
                            <Setter TargetName="SubMenuArrow" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 子菜单打开状态 -->
                        <Trigger Property="IsSubmenuOpen" Value="True">
                            <Setter TargetName="Border" Property="Background" 
                                    Value="{DynamicResource ControlFillColorDefaultHoverBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 菜单阴影效果 -->
    <DropShadowEffect x:Key="MenuShadowEffect"
                      Color="{DynamicResource SystemBaseColor50}"
                      Direction="270"
                      ShadowDepth="4"
                      BlurRadius="12"
                      Opacity="0.3"/>

</ResourceDictionary>
