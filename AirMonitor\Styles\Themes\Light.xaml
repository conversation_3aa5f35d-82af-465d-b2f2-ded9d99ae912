<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Light Theme - 亮色主题
         符合Windows 11 Fluent Design亮色主题规范
         ======================================== -->

    <!-- 合并基础色彩定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="../Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- 主色调画刷 (Primary Brushes) -->
    <SolidColorBrush x:Key="SystemAccentBrush" Color="{StaticResource SystemAccentColorPrimary}"/>
    <SolidColorBrush x:Key="SystemAccentBrushSecondary" Color="{StaticResource SystemAccentColorSecondary}"/>
    <SolidColorBrush x:Key="SystemAccentBrushTertiary" Color="{StaticResource SystemAccentColorTertiary}"/>
    
    <!-- 主色调变体画刷 -->
    <SolidColorBrush x:Key="SystemAccentBrushLight1" Color="{StaticResource SystemAccentColorLight1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight2" Color="{StaticResource SystemAccentColorLight2}"/>
    <SolidColorBrush x:Key="SystemAccentBrushLight3" Color="{StaticResource SystemAccentColorLight3}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark1" Color="{StaticResource SystemAccentColorDark1}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark2" Color="{StaticResource SystemAccentColorDark2}"/>
    <SolidColorBrush x:Key="SystemAccentBrushDark3" Color="{StaticResource SystemAccentColorDark3}"/>

    <!-- 背景画刷 (Background Brushes) -->
    <SolidColorBrush x:Key="ApplicationBackgroundBrush" Color="{StaticResource SystemBaseColorHigh}"/>
    <SolidColorBrush x:Key="LayerBackgroundBrush" Color="{StaticResource SystemBaseColorMediumHigh}"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="{StaticResource SystemBaseColorHigh}"/>
    <SolidColorBrush x:Key="SurfaceBackgroundBrush" Color="{StaticResource SystemBaseColorMedium}"/>
    <SolidColorBrush x:Key="SubtleBackgroundBrush" Color="{StaticResource SystemBaseColorMediumLow}"/>
    
    <!-- 文本画刷 (Text Brushes) -->
    <SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource SystemBaseForegroundColorHigh}"/>
    <SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource SystemBaseForegroundColorMediumHigh}"/>
    <SolidColorBrush x:Key="TextFillColorTertiaryBrush" Color="{StaticResource SystemBaseForegroundColorMedium}"/>
    <SolidColorBrush x:Key="TextFillColorDisabledBrush" Color="{StaticResource SystemBaseForegroundColorMediumLow}"/>
    <SolidColorBrush x:Key="TextFillColorInverseBrush" Color="{StaticResource SystemBaseColorHigh}"/>
    
    <!-- 边框画刷 (Border Brushes) -->
    <SolidColorBrush x:Key="ControlStrokeColorDefaultBrush" Color="{StaticResource SystemBorderColorHigh}"/>
    <SolidColorBrush x:Key="ControlStrokeColorSecondaryBrush" Color="{StaticResource SystemBorderColorMedium}"/>
    <SolidColorBrush x:Key="ControlStrokeColorTertiaryBrush" Color="{StaticResource SystemBorderColorLow}"/>
    <SolidColorBrush x:Key="DividerStrokeColorDefaultBrush" Color="{StaticResource SystemBorderColorMedium}"/>
    
    <!-- 控件填充画刷 (Control Fill Brushes) -->
    <SolidColorBrush x:Key="ControlFillColorDefaultBrush" Color="{StaticResource SystemBaseColorMediumHigh}"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryBrush" Color="{StaticResource SystemBaseColorMedium}"/>
    <SolidColorBrush x:Key="ControlFillColorTertiaryBrush" Color="{StaticResource SystemBaseColorMediumLow}"/>
    <SolidColorBrush x:Key="ControlFillColorDisabledBrush" Color="{StaticResource SystemBaseColorLow}"/>
    <SolidColorBrush x:Key="ControlFillColorInputActiveBrush" Color="{StaticResource SystemBaseColorHigh}"/>
    
    <!-- 语义色画刷 (Semantic Brushes) -->
    <SolidColorBrush x:Key="SystemFillColorSuccessBrush" Color="{StaticResource SystemSuccessColor}"/>
    <SolidColorBrush x:Key="SystemFillColorSuccessBackgroundBrush" Color="{StaticResource SystemSuccessColorLight}"/>
    <SolidColorBrush x:Key="SystemFillColorWarningBrush" Color="{StaticResource SystemWarningColor}"/>
    <SolidColorBrush x:Key="SystemFillColorWarningBackgroundBrush" Color="{StaticResource SystemWarningColorLight}"/>
    <SolidColorBrush x:Key="SystemFillColorCriticalBrush" Color="{StaticResource SystemErrorColor}"/>
    <SolidColorBrush x:Key="SystemFillColorCriticalBackgroundBrush" Color="{StaticResource SystemErrorColorLight}"/>
    <SolidColorBrush x:Key="SystemFillColorNeutralBrush" Color="{StaticResource SystemInfoColor}"/>
    <SolidColorBrush x:Key="SystemFillColorNeutralBackgroundBrush" Color="{StaticResource SystemInfoColorLight}"/>
    
    <!-- 透明度画刷 (Opacity Brushes) -->
    <SolidColorBrush x:Key="SystemAccentBrush10" Color="{StaticResource SystemAccentColor10}"/>
    <SolidColorBrush x:Key="SystemAccentBrush20" Color="{StaticResource SystemAccentColor20}"/>
    <SolidColorBrush x:Key="SystemAccentBrush30" Color="{StaticResource SystemAccentColor30}"/>
    <SolidColorBrush x:Key="SystemAccentBrush40" Color="{StaticResource SystemAccentColor40}"/>
    <SolidColorBrush x:Key="SystemAccentBrush50" Color="{StaticResource SystemAccentColor50}"/>
    <SolidColorBrush x:Key="SystemAccentBrush60" Color="{StaticResource SystemAccentColor60}"/>
    <SolidColorBrush x:Key="SystemAccentBrush70" Color="{StaticResource SystemAccentColor70}"/>
    <SolidColorBrush x:Key="SystemAccentBrush80" Color="{StaticResource SystemAccentColor80}"/>
    <SolidColorBrush x:Key="SystemAccentBrush90" Color="{StaticResource SystemAccentColor90}"/>
    
    <SolidColorBrush x:Key="SystemBaseBrush10" Color="{StaticResource SystemBaseColor10}"/>
    <SolidColorBrush x:Key="SystemBaseBrush20" Color="{StaticResource SystemBaseColor20}"/>
    <SolidColorBrush x:Key="SystemBaseBrush30" Color="{StaticResource SystemBaseColor30}"/>
    <SolidColorBrush x:Key="SystemBaseBrush40" Color="{StaticResource SystemBaseColor40}"/>
    <SolidColorBrush x:Key="SystemBaseBrush50" Color="{StaticResource SystemBaseColor50}"/>
    <SolidColorBrush x:Key="SystemBaseBrush60" Color="{StaticResource SystemBaseColor60}"/>
    <SolidColorBrush x:Key="SystemBaseBrush70" Color="{StaticResource SystemBaseColor70}"/>
    <SolidColorBrush x:Key="SystemBaseBrush80" Color="{StaticResource SystemBaseColor80}"/>
    <SolidColorBrush x:Key="SystemBaseBrush90" Color="{StaticResource SystemBaseColor90}"/>

    <!-- 悬停状态画刷 (Hover State Brushes) -->
    <SolidColorBrush x:Key="ControlFillColorDefaultHoverBrush" Color="{StaticResource SystemBaseColor30}"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryHoverBrush" Color="{StaticResource SystemBaseColor40}"/>
    <SolidColorBrush x:Key="SystemAccentBrushHover" Color="{StaticResource SystemAccentColorSecondary}"/>
    
    <!-- 按下状态画刷 (Pressed State Brushes) -->
    <SolidColorBrush x:Key="ControlFillColorDefaultPressedBrush" Color="{StaticResource SystemBaseColor50}"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryPressedBrush" Color="{StaticResource SystemBaseColor60}"/>
    <SolidColorBrush x:Key="SystemAccentBrushPressed" Color="{StaticResource SystemAccentColorTertiary}"/>
    
    <!-- 焦点状态画刷 (Focus State Brushes) -->
    <SolidColorBrush x:Key="FocusStrokeColorOuterBrush" Color="{StaticResource SystemBaseForegroundColorHigh}"/>
    <SolidColorBrush x:Key="FocusStrokeColorInnerBrush" Color="{StaticResource SystemBaseColorHigh}"/>

</ResourceDictionary>
