﻿using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using AirMonitor.Services;

namespace AirMonitor;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    /// <summary>
    /// 服务提供者
    /// </summary>
    public IServiceProvider? ServiceProvider => _host?.Services;

    protected override void OnStartup(StartupEventArgs e)
    {
        // 配置依赖注入容器
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(ConfigureServices)
            .Build();

        // 初始化主题服务
        var themeService = _host.Services.GetRequiredService<IThemeService>();
        themeService.Initialize();

        base.OnStartup(e);

        // 显示主窗口
        var mainWindow = new MainWindow();
        mainWindow.Show();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // 注册主题服务
        services.AddSingleton<IThemeService, ThemeService>();

        // 在这里可以注册其他服务
        // services.AddTransient<IDataService, DataService>();
        // services.AddTransient<MainViewModel>();
    }
}
